import type {
  ChatGroupInfo,
  SendGroupMessageParams,
} from './types';
import { SimpleHttpClient } from './request';

interface BotApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data: T;
}
// Bot API 地址：开发环境使用代理，生产环境直接访问 https://bot.jsj-ai.com
const botHttpClient = new SimpleHttpClient(import.meta.env.VITE_BOT_API_BASE_URL || '/demo');

// ==================== 群发消息相关API ====================

/**
 * 获取群列表
 * 说明：通过此接口可以获取所有当前登录精算家平台的授权企业的企业微信所有外部群列表
 */
export async function fetchChatGroupList(): Promise<ChatGroupInfo[]> {
  const response = await botHttpClient.get<BotApiResponse<ChatGroupInfo[]>>('/listAllChatGroup');
  if (response.success && response.code === 200) {
    return response.data || [];
  }
  throw new Error(response.message || '获取群列表失败');
}

/**
 * 群发消息
 * 描述：上个接口返回的群聊列表，在前端可以勾选，勾选之后封装成一个chatIdList，然后和发送的消息文本一起组装成一个json请求后端
 */
export async function sendGroupMessage(data: SendGroupMessageParams): Promise<string> {
  const response = await botHttpClient.post<BotApiResponse<string>>('/sendMsg', data);
  if (response.success && response.code === 200) {
    return response.data || '发送成功';
  }
  throw new Error(response.message || '发送消息失败');
}
